@extends('layouts.admin')

@section('title', 'Reservations - SMP Online')

@section('content')
    <!-- Page Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Reservations Management</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Reservations</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- Page Header Close -->

    <!-- Livewire Reservations List Component -->
    @livewire('reservations-list')

    <!-- Restore Reservation Confirmation Modal -->
    <x-confirmation-modal
        modal-id="restoreReservationModal"
        type="success"
        icon="bx bx-undo"
        modal-title="Confirm Restore"
        title="Are you sure you want to restore the reservation for &quot;<span id='restoreReservationDetails' class='fw-semibold'></span>&quot;?"
        warning-text="This will reactivate the reservation and make it available again."
        cancel-text="No, Keep Cancelled"
        confirm-text="Yes, Restore Reservation"
        form-action="#"
        form-method="POST"
    />

    <!-- Cancel Reservation Confirmation Modal -->
    <x-confirmation-modal
        modal-id="cancelReservationModal"
        type="danger"
        icon="ri-alert-fill"
        modal-title="Confirm Cancel"
        title="Are you sure you want to cancel the reservation for &quot;<span id='cancelReservationDetails' class='fw-semibold'></span>&quot;?"
        warning-text="The reservation will be cancelled."
        cancel-text="No, Keep Reservation"
        confirm-text="Yes, Cancel Reservation"
        form-action="#"
        form-method="POST"
    />
@endsection

@push('scripts')
    <script>
        // Variables to store current reservation data for modals
        let currentReservationId = null;
        let currentReservationDetails = '';

        // Make table row clickable
        document.addEventListener('DOMContentLoaded', function() {
            // Use event delegation for dynamically loaded content
            document.addEventListener('click', function(e) {
                const row = e.target.closest('.clickable-row');
                if (row && !e.target.closest('a') && !e.target.closest('button') && !e.target.closest('form')) {
                    const url = row.dataset.url;
                    if (url) {
                        window.location.href = url;
                    }
                }
            });
        });

        // Listen for Livewire events
        document.addEventListener('livewire:init', function() {
            // Listen for confirm-cancel event
            window.Livewire.on('confirm-cancel', (data) => {
                currentReservationId = data[0].reservationId;
                currentReservationDetails = data[0].reservationDetails;

                document.getElementById('cancelReservationDetails').textContent = currentReservationDetails;
                const modal = new bootstrap.Modal(document.getElementById('cancelReservationModal'));
                modal.show();
            });

            // Listen for confirm-restore event
            window.Livewire.on('confirm-restore', (data) => {
                currentReservationId = data[0].reservationId;
                currentReservationDetails = data[0].reservationDetails;

                document.getElementById('restoreReservationDetails').textContent = currentReservationDetails;
                const modal = new bootstrap.Modal(document.getElementById('restoreReservationModal'));
                modal.show();
            });

            // Listen for hide-message-after-delay event
            window.Livewire.on('hide-message-after-delay', () => {
                setTimeout(() => {
                    window.Livewire.dispatch('hideMessages');
                }, 5000);
            });
        });

        // Handle modal confirm buttons
        document.addEventListener('DOMContentLoaded', function() {
            // Wait for modals to be available
            setTimeout(() => {
                // Cancel reservation confirm button
                const cancelConfirmBtn = document.querySelector('#cancelReservationModal .btn-danger');
                if (cancelConfirmBtn) {
                    cancelConfirmBtn.addEventListener('click', function() {
                        if (currentReservationId) {
                            window.Livewire.dispatch('cancelReservation', { reservationId: currentReservationId });
                            bootstrap.Modal.getInstance(document.getElementById('cancelReservationModal')).hide();
                        }
                    });
                }

                // Restore reservation confirm button
                const restoreConfirmBtn = document.querySelector('#restoreReservationModal .btn-success');
                if (restoreConfirmBtn) {
                    restoreConfirmBtn.addEventListener('click', function() {
                        if (currentReservationId) {
                            window.Livewire.dispatch('restoreReservation', { reservationId: currentReservationId });
                            bootstrap.Modal.getInstance(document.getElementById('restoreReservationModal')).hide();
                        }
                    });
                }
            }, 100);
        });
    </script>
@endpush
