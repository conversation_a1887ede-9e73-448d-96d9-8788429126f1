<div>
    <!-- Success/Error Messages -->
    @if ($showSuccessMessage)
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="ti ti-check-circle me-2"></i>{{ $successMessage }}
            <button type="button" class="btn-close" wire:click="hideMessages" aria-label="Close"></button>
        </div>
    @endif

    @if ($showErrorMessage)
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="ti ti-exclamation-triangle me-2"></i>{{ $errorMessage }}
            <button type="button" class="btn-close" wire:click="hideMessages" aria-label="Close"></button>
        </div>
    @endif

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="d-flex align-items-top">
                        <div class="me-3">
                            <span class="avatar avatar-md bg-primary-transparent">
                                <i class="ti ti-calendar-event fs-16"></i>
                            </span>
                        </div>
                        <div class="flex-fill">
                            <div class="d-flex align-items-center justify-content-between mb-1">
                                <h6 class="mb-0">Upcoming Reservations</h6>
                            </div>
                            <div class="d-flex align-items-center justify-content-between">
                                <div>
                                    <span class="h4 fw-semibold mb-0">{{ $upcomingCount }}</span>
                                </div>
                                <div class="text-success">
                                    <i class="ti ti-trending-up me-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="d-flex align-items-top">
                        <div class="me-3">
                            <span class="avatar avatar-md bg-success-transparent">
                                <i class="ti ti-check fs-16"></i>
                            </span>
                        </div>
                        <div class="flex-fill">
                            <div class="d-flex align-items-center justify-content-between mb-1">
                                <h6 class="mb-0">Total Reservations</h6>
                            </div>
                            <div class="d-flex align-items-center justify-content-between">
                                <div>
                                    <span class="h4 fw-semibold mb-0">{{ $totalReservationsCount }}</span>
                                </div>
                                <div class="text-success">
                                    <i class="ti ti-trending-up me-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Reservations List -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        <i class="ti ti-list me-2"></i>Reservations
                    </div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('reservations.create') }}" class="btn btn-primary btn-sm">
                            <i class="ti ti-plus me-1"></i>New Reservation
                        </a>
                        <a href="{{ route('calendar.index') }}" class="btn btn-success btn-sm">
                            <i class="ti ti-calendar me-1"></i>View Calendar
                        </a>
                    </div>
                </div>

                <!-- Search and Filter Form -->
                <div class="px-4">
                    <div class="row bg-light rounded mt-3 mb-0 py-2 align-items-center">
                        <div class="col-xl-3">
                            <div class="d-flex align-items-center gap-2">
                                <label class="form-label mb-0 fw-semibold text-muted">
                                    <i class="ti ti-filter me-1"></i>Filters:
                                </label>
                                <select wire:model.live="status" class="form-select">
                                    <option value="">All Statuses</option>
                                    @foreach (\App\Models\Booking::getStatuses() as $key => $statusOption)
                                        <option value="{{ $key }}">{{ $statusOption }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-xl-2">
                            <select wire:model.live="field_id" class="form-select">
                                <option value="">All Fields</option>
                                @foreach ($fields as $field)
                                    <option value="{{ $field->id }}">{{ $field->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-xl-2">
                            <input type="date" wire:model.live="date_from" class="form-control pt-1 pb-1" placeholder="From Date">
                        </div>
                        <div class="col-xl-2">
                            <input type="date" wire:model.live="date_to" class="form-control pt-1 pb-1" placeholder="To Date">
                        </div>
                        <div class="col-xl-3 mt-0">
                            <div class="d-flex gap-2">
                                <button type="button" wire:click="clearFilters" class="btn btn-info pt-1 pb-1 flex-fill">
                                    <i class="ti ti-refresh me-1"></i>Clear
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    @if ($reservations->count() > 0)
                        <div class="table-responsive">
                            <table class="table text-nowrap table-hover">
                                <thead>
                                    <tr>
                                        <th>Field</th>
                                        <th>Date & Time</th>
                                        <th>Customer</th>
                                        <th>Cost</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($reservations as $reservation)
                                        <tr class="clickable-row" data-url="{{ route('reservations.show', $reservation) }}">
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="me-2">
                                                        <span class="avatar avatar-sm bg-dark-transparent text-dark">
                                                            <i class="{{ $reservation->field->icon ?? 'bx bx-stadium' }}" style="font-size: 1.2rem;"></i>
                                                        </span>
                                                    </div>
                                                    <div>
                                                        <span class="fw-semibold">{{ $reservation->field->name }}</span>
                                                        <br>
                                                        <small class="text-muted">{{ $reservation->field->type }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <span class="fw-semibold">{{ $reservation->booking_date->format('M d, Y') }}</span>
                                                    <br>
                                                    <small class="text-muted">{{ $reservation->time_range }}</small>
                                                    <br>
                                                    <span class="badge bg-primary-transparent text-primary">Duration:
                                                        {{ $reservation->duration_hours }}
                                                        {{ Str::plural('hour', $reservation->duration_hours) }}</span>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="fw-semibold">{{ $reservation->customer_display_name }}</span>
                                            </td>
                                            <td>
                                                <span class="fw-semibold text-success">XCG {{ number_format($reservation->total_cost, 2) }}</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ $reservation->status_color }}-transparent text-{{ $reservation->status_color }}">
                                                    {{ $reservation->status }}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    {{-- View Details: Allow admins, members, and reservation owners --}}
                                                    @if (auth()->user()->isAdmin() || auth()->user()->isMember() || $reservation->user_id === auth()->id())
                                                        <a href="{{ route('reservations.show', $reservation) }}" class="btn btn-sm btn-info" title="View Details">
                                                            <i class="ti ti-eye"></i>
                                                        </a>
                                                    @endif

                                                    {{-- Edit/Cancel Actions: Only for admins and reservation owners --}}
                                                    @if (auth()->user()->isAdmin() || $reservation->user_id === auth()->id())
                                                        @if ($reservation->canBeModified())
                                                            <a href="{{ route('reservations.edit', $reservation) }}" class="btn btn-sm btn-warning" title="Edit">
                                                                <i class="ti ti-edit"></i>
                                                            </a>
                                                        @endif

                                                        {{-- Cancel/Restore Actions --}}
                                                        @if ($reservation->isCancelled() && $reservation->canBeUncancelled())
                                                            <button type="button"
                                                                class="btn btn-sm btn-primary"
                                                                title="Restore"
                                                                wire:click="$dispatch('confirm-restore', { reservationId: {{ $reservation->id }}, reservationDetails: '{{ $reservation->field->name }} - {{ $reservation->booking_date->format('M j, Y') }} {{ $reservation->time_range }}' })"
                                                                wire:loading.attr="disabled"
                                                                wire:target="restoreReservation({{ $reservation->id }})">
                                                                @if ($restoringReservationId === $reservation->id)
                                                                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                                @else
                                                                    <i class="bx bx-undo"></i>
                                                                @endif
                                                            </button>
                                                        @elseif ($reservation->canBeCancelled())
                                                            <button type="button"
                                                                class="btn btn-sm btn-danger"
                                                                title="Cancel"
                                                                wire:click="$dispatch('confirm-cancel', { reservationId: {{ $reservation->id }}, reservationDetails: '{{ $reservation->field->name }} - {{ $reservation->booking_date->format('M j, Y') }} {{ $reservation->time_range }}' })"
                                                                wire:loading.attr="disabled"
                                                                wire:target="cancelReservation({{ $reservation->id }})">
                                                                @if ($cancellingReservationId === $reservation->id)
                                                                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                                @else
                                                                    <i class="ti ti-x"></i>
                                                                @endif
                                                            </button>
                                                        @endif
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="booking-pagination">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="pagination-info">
                                    Showing {{ $reservations->firstItem() }} to {{ $reservations->lastItem() }}
                                    of {{ $reservations->total() }} reservations
                                </div>
                                <div class="admin-pagination">
                                    {{ $reservations->links() }}
                                </div>
                            </div>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <div class="mb-3">
                                <i class="ti ti-calendar-x fs-48 text-muted"></i>
                            </div>

                            @if ($status || $field_id || $date_from || $date_to)
                                <h5 class="text-muted">No Reservations Match Your Filters</h5>
                                <p class="text-muted">Try adjusting the filter options.</p>
                                <button wire:click="clearFilters" class="btn btn-secondary">
                                    <i class="ti ti-refresh me-1"></i>Clear Filters
                                </button>
                            @else
                                <h5 class="text-muted">No Reservations Found</h5>
                                <p class="text-muted">You haven't made any reservations yet.</p>
                                <a href="{{ route('reservations.create') }}" class="btn btn-primary">
                                    <i class="ti ti-plus me-1"></i>Make Your First Reservation
                                </a>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
