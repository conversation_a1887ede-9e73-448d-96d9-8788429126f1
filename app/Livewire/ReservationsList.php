<?php

namespace App\Livewire;

use App\Models\Field;
use App\Models\Reservation;
use Carbon\Carbon;
use Livewire\Component;
use Livewire\WithPagination;

class ReservationsList extends Component
{
    use WithPagination;

    // Filter properties
    public $status = '';
    public $field_id = '';
    public $date_from = '';
    public $date_to = '';

    // UI state properties
    public $showSuccessMessage = false;
    public $showErrorMessage = false;
    public $successMessage = '';
    public $errorMessage = '';

    // Loading states
    public $cancellingReservationId = null;
    public $restoringReservationId = null;

    protected $paginationTheme = 'bootstrap';

    protected $queryString = [
        'status' => ['except' => ''],
        'field_id' => ['except' => ''],
        'date_from' => ['except' => ''],
        'date_to' => ['except' => ''],
        'page' => ['except' => 1],
    ];

    protected $listeners = [
        'cancelReservation',
        'restoreReservation',
        'hideMessages',
    ];

    public function mount()
    {
        // Initialize filters from request
        $this->status = request('status', '');
        $this->field_id = request('field_id', '');
        $this->date_from = request('date_from', '');
        $this->date_to = request('date_to', '');
    }

    public function updatedStatus()
    {
        $this->resetPage();
    }

    public function updatedFieldId()
    {
        $this->resetPage();
    }

    public function updatedDateFrom()
    {
        $this->resetPage();
    }

    public function updatedDateTo()
    {
        $this->resetPage();
    }

    public function clearFilters()
    {
        $this->status = '';
        $this->field_id = '';
        $this->date_from = '';
        $this->date_to = '';
        $this->resetPage();
    }

    public function cancelReservation($data)
    {
        $reservationId = $data['reservationId'] ?? $data;

        try {
            $this->cancellingReservationId = $reservationId;

            $reservation = Reservation::findOrFail($reservationId);
            $user = auth()->user();

            // Authorization check: Allow admins or reservation owners
            if (!$user->isAdmin() && $reservation->user_id !== auth()->id()) {
                $this->showError('You can only cancel your own reservations.');
                return;
            }

            if (!$reservation->canBeCancelled()) {
                $this->showError('This reservation cannot be cancelled. Reservations can only be cancelled up to 24 hours before the scheduled time.');
                return;
            }

            $reservation->cancel();
            $this->showSuccess('Reservation cancelled successfully.');

        } catch (\Exception $e) {
            $this->showError('An error occurred while cancelling the reservation.');
        } finally {
            $this->cancellingReservationId = null;
        }
    }

    public function restoreReservation($data)
    {
        $reservationId = $data['reservationId'] ?? $data;

        try {
            $this->restoringReservationId = $reservationId;

            $reservation = Reservation::findOrFail($reservationId);
            $user = auth()->user();

            // Authorization check: Allow admins or reservation owners
            if (!$user->isAdmin() && $reservation->user_id !== auth()->id()) {
                $this->showError('You can only restore your own reservations.');
                return;
            }

            if (!$reservation->isCancelled()) {
                $this->showError('This reservation is not cancelled.');
                return;
            }

            $reservation->uncancel();
            $this->showSuccess('Reservation restored successfully.');

        } catch (\Exception $e) {
            $this->showError('An error occurred while restoring the reservation.');
        } finally {
            $this->restoringReservationId = null;
        }
    }

    private function showSuccess($message)
    {
        $this->successMessage = $message;
        $this->showSuccessMessage = true;
        $this->showErrorMessage = false;

        // Auto-hide after 5 seconds
        $this->dispatch('hide-message-after-delay');
    }

    private function showError($message)
    {
        $this->errorMessage = $message;
        $this->showErrorMessage = true;
        $this->showSuccessMessage = false;

        // Auto-hide after 5 seconds
        $this->dispatch('hide-message-after-delay');
    }

    public function hideMessages()
    {
        $this->showSuccessMessage = false;
        $this->showErrorMessage = false;
    }

    public function render()
    {
        // Update completed reservations
        $today = Carbon::today();
        Reservation::where('status', 'Confirmed')
            ->whereDate('booking_date', '<', $today)
            ->update(['status' => 'Completed']);

        // Build query
        $query = Reservation::with('field', 'user');

        // Apply filters
        if ($this->status) {
            $query->where('status', $this->status);
        }

        if ($this->date_from) {
            $query->whereDate('booking_date', '>=', Carbon::parse($this->date_from)->toDateString());
        }

        if ($this->date_to) {
            $inclusiveEnd = Carbon::parse($this->date_to)->addDay()->toDateString();
            $query->whereDate('booking_date', '<', $inclusiveEnd);
        }

        if ($this->field_id) {
            $query->where('field_id', $this->field_id);
        }

        // Get paginated reservations
        $reservations = $query
            ->orderBy('booking_date', 'desc')
            ->orderBy('start_time', 'desc')
            ->paginate(10);

        // Get statistics
        $upcomingCount = Reservation::upcoming()->active()->count();
        $totalReservationsCount = Reservation::count();

        // Get fields for filter dropdown
        $fields = Field::active()->orderBy('name')->get();

        return view('livewire.reservations-list', [
            'reservations' => $reservations,
            'upcomingCount' => $upcomingCount,
            'totalReservationsCount' => $totalReservationsCount,
            'fields' => $fields,
        ]);
    }
}
